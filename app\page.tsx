"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2, FileText, User, Briefcase } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { ProfileSummary } from "./components/profile-summary"
import { PDFGenerator } from "./components/pdf-generator"

interface PersonalInfo {
  fullName: string
  email: string
  phone: string
  location: string
  linkedIn: string
  portfolio: string
  summary: string
  skills: string
  experience: string
  education: string
  certifications: string
}

interface GeneratedContent {
  resume: string
  coverLetter: string
}

export default function ResumeGenerator() {
  const [personalInfo, setPersonalInfo] = useState<PersonalInfo>({
    fullName: "Tishbian Meshach S",
    email: "<EMAIL>",
    phone: "+91 7339258486",
    location: "Tuticorin, Tamil Nadu, India",
    linkedIn: "https://linkedin.com/in/tishbian-meshach",
    portfolio: "https://tishbian.vercel.app",
    summary:
      "Innovative UI/UX Designer and AI-Enhanced Graphic Designer with 4+ years of experience in creating user-centered designs and visual content. Expert in leveraging artificial intelligence and prompt engineering to optimize design workflows and enhance creative output. Currently pursuing Computer Science Engineering while leading design teams and pioneering AI-integrated design processes. Proven track record of increasing productivity by 60% through strategic AI implementation and automated design systems.",
    skills:
      "UI/UX Design, Graphic Design, Figma, Adobe Creative Suite, Adobe Photoshop, Adobe Illustrator, After Effects, Premiere Pro, React, JavaScript, HTML5, CSS3, Tailwind CSS, Prototyping, User Research, Usability Testing, Wireframing, Adobe XD, Sketch, Artificial Intelligence, AI-Powered Design, Prompt Engineering, ChatGPT, Claude, Gemini AI, Midjourney, DALL-E, Stable Diffusion, AI Workflow Automation, Design Process Optimization, AI Content Generation, Machine Learning for Design, AI-Assisted Prototyping, Automated Design Systems",
    experience: `Leading Designer | TripXplo | 2023 - Present | Full-time
• Lead design team in developing user-centred designs for web and mobile applications
• Conduct user research and usability testing to inform design decisions
• Create wireframes, prototypes, and high-fidelity designs using Sketch, Figma, and Adobe XD
• Implement AI-powered design workflows to enhance productivity by 60%
• Utilize prompt engineering to generate design concepts and user personas
• Integrate AI tools for automated design system creation and maintenance
• Collaborate with development teams using AI-assisted documentation
• Enhanced user engagement through AI-optimized UX design strategies
• Reduced design iteration time by 50% using AI-powered prototyping tools
• Successfully led cross-functional design projects with AI workflow integration

AI-Enhanced Freelance Graphic Designer | Self-Employed | 2019 - Present | Freelance
• Designed custom graphics and visual content for YouTubers and content creators
• Created thumbnails, banners, logos, and brand identity assets using AI-assisted workflows
• Developed automated design processes using prompt engineering techniques
• Managed multiple projects simultaneously with AI-powered project management
• Enhanced client brand identity through AI-generated design variations and A/B testing
• Utilized generative AI for rapid concept development and ideation
• Successfully completed 100+ design projects with 40% faster turnaround using AI
• Maintained 100% client satisfaction rate through AI-enhanced quality control
• Specialized in YouTube content creator branding with AI-optimized thumbnails
• Increased design output by 3x through strategic AI tool integration`,
    education:
      "Bachelor of Engineering (B.E) in Computer Science | University College of Engineering, Kanchipuram | 2022-2026 | Pursuing",
    certifications:
      "AI for Designers (Self-taught), Advanced Prompt Engineering Techniques, Machine Learning for Creative Applications",
  })

  const [jobDescription, setJobDescription] = useState("")
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handlePersonalInfoChange = (field: keyof PersonalInfo, value: string) => {
    setPersonalInfo((prev) => ({ ...prev, [field]: value }))
  }

  const generateContent = async () => {
    if (!jobDescription.trim()) {
      toast({
        title: "Error",
        description: "Please paste a job description",
        variant: "destructive",
      })
      return
    }

    if (!personalInfo.fullName || !personalInfo.email) {
      toast({
        title: "Error",
        description: "Please fill in at least your name and email",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch("/api/generate-content", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          personalInfo,
          jobDescription,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to generate content")
      }

      const data = await response.json()
      setGeneratedContent(data)

      toast({
        title: "Success!",
        description: "Resume and cover letter generated successfully",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate content. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${type} copied to clipboard`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
      })
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">AI Resume & Cover Letter Generator</h1>
          <p className="text-lg text-gray-600">
            Create ATS-friendly resumes and personalized cover letters using Gemini AI
          </p>
        </div>

        <ProfileSummary />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
                <CardDescription>Fill in your details to personalize your resume and cover letter</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="fullName">Full Name *</Label>
                    <Input
                      id="fullName"
                      value={personalInfo.fullName}
                      onChange={(e) => handlePersonalInfoChange("fullName", e.target.value)}
                      placeholder="John Doe"
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={personalInfo.email}
                      onChange={(e) => handlePersonalInfoChange("email", e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                      id="phone"
                      value={personalInfo.phone}
                      onChange={(e) => handlePersonalInfoChange("phone", e.target.value)}
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={personalInfo.location}
                      onChange={(e) => handlePersonalInfoChange("location", e.target.value)}
                      placeholder="New York, NY"
                    />
                  </div>
                  <div>
                    <Label htmlFor="linkedIn">LinkedIn</Label>
                    <Input
                      id="linkedIn"
                      value={personalInfo.linkedIn}
                      onChange={(e) => handlePersonalInfoChange("linkedIn", e.target.value)}
                      placeholder="linkedin.com/in/johndoe"
                    />
                  </div>
                  <div>
                    <Label htmlFor="portfolio">Portfolio/Website</Label>
                    <Input
                      id="portfolio"
                      value={personalInfo.portfolio}
                      onChange={(e) => handlePersonalInfoChange("portfolio", e.target.value)}
                      placeholder="johndoe.com"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="summary">Professional Summary</Label>
                  <Textarea
                    id="summary"
                    value={personalInfo.summary}
                    onChange={(e) => handlePersonalInfoChange("summary", e.target.value)}
                    placeholder="Brief professional summary highlighting your key strengths..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="skills">Skills</Label>
                  <Textarea
                    id="skills"
                    value={personalInfo.skills}
                    onChange={(e) => handlePersonalInfoChange("skills", e.target.value)}
                    placeholder="List your technical and soft skills, separated by commas..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="experience">Work Experience</Label>
                  <Textarea
                    id="experience"
                    value={personalInfo.experience}
                    onChange={(e) => handlePersonalInfoChange("experience", e.target.value)}
                    placeholder="List your work experience with company names, positions, dates, and key achievements..."
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="education">Education</Label>
                  <Textarea
                    id="education"
                    value={personalInfo.education}
                    onChange={(e) => handlePersonalInfoChange("education", e.target.value)}
                    placeholder="Your educational background, degrees, institutions, and dates..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="certifications">Certifications</Label>
                  <Textarea
                    id="certifications"
                    value={personalInfo.certifications}
                    onChange={(e) => handlePersonalInfoChange("certifications", e.target.value)}
                    placeholder="Any relevant certifications, licenses, or additional qualifications..."
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5" />
                  Job Description
                </CardTitle>
                <CardDescription>Paste the job description to tailor your resume and cover letter</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={jobDescription}
                  onChange={(e) => setJobDescription(e.target.value)}
                  placeholder="Paste the complete job description here..."
                  rows={8}
                  className="w-full"
                />
                <Button onClick={generateContent} disabled={isLoading} className="w-full mt-4" size="lg">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Content...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" />
                      Generate Resume & Cover Letter
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Output Section */}
          <div className="space-y-6">
            {generatedContent ? (
              <Card>
                <CardHeader>
                  <CardTitle>Generated Content</CardTitle>
                  <CardDescription>Your personalized ATS-friendly resume and cover letter</CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="resume" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="resume">Resume</TabsTrigger>
                      <TabsTrigger value="pdf">PDF Preview</TabsTrigger>
                      <TabsTrigger value="cover-letter">Cover Letter</TabsTrigger>
                    </TabsList>

                    <TabsContent value="resume" className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">ATS-Friendly Resume</h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(generatedContent.resume, "Resume")}
                        >
                          Copy Resume
                        </Button>
                      </div>
                      <div className="bg-white p-6 rounded-lg border max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm font-mono">{generatedContent.resume}</pre>
                      </div>
                    </TabsContent>

                    <TabsContent value="pdf" className="space-y-4">
                      <PDFGenerator
                        resumeContent={generatedContent.resume}
                        personalInfo={{
                          fullName: personalInfo.fullName,
                          email: personalInfo.email,
                          phone: personalInfo.phone,
                          location: personalInfo.location,
                          linkedIn: personalInfo.linkedIn,
                          portfolio: personalInfo.portfolio,
                        }}
                      />
                    </TabsContent>

                    <TabsContent value="cover-letter" className="space-y-4">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-semibold">Personalized Cover Letter</h3>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(generatedContent.coverLetter, "Cover Letter")}
                        >
                          Copy Cover Letter
                        </Button>
                      </div>
                      <div className="bg-white p-6 rounded-lg border max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm">{generatedContent.coverLetter}</pre>
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">No Content Generated Yet</h3>
                  <p className="text-gray-500 text-center">
                    Fill in your personal information and paste a job description, then click "Generate" to create your
                    customized resume and cover letter.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
