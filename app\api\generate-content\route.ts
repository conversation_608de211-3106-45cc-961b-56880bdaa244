import { type NextRequest, NextResponse } from "next/server"

export const maxDuration = 60

interface PersonalInfo {
  fullName: string
  email: string
  phone: string
  location: string
  linkedIn: string
  portfolio: string
  summary: string
  skills: string
  experience: string
  education: string
  certifications: string
}

interface RequestBody {
  personalInfo: PersonalInfo
  jobDescription: string
}

// Google Generative AI API configuration
const GOOGLE_API_KEY = process.env.GOOGLE_GENERATIVE_AI_API_KEY || "AIzaSyAkhvYwxNCvSgv-afNAGOjq5y61pDCymMk"
const GOOGLE_API_URL = `https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=${GOOGLE_API_KEY}`

// Target keywords for design internships
const INCLUDE_KEYWORDS = [
  "Graphic Design Intern",
  "UI/UX Intern",
  "Design Intern",
  "Visual Design Intern",
  "Product Design Intern",
]

const EXCLUDE_KEYWORDS = ["Senior", "Lead", "Manager", "Director", "unpaid"]

const USER_SKILLS = [
  "Figma",
  "Adobe XD",
  "Photoshop",
  "Illustrator",
  "HTML",
  "CSS",
  "UI Design",
  "UX Design",
  "AI-Powered Design",
  "Prompt Engineering",
  "ChatGPT",
  "Midjourney",
  "DALL-E",
]

async function generateWithGemini(prompt: string): Promise<string> {
  try {
    const response = await fetch(GOOGLE_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt,
              },
            ],
          },
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 3000,
        },
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error("Google API Error:", errorData)
      throw new Error(`Google API request failed: ${response.status}`)
    }

    const data = await response.json()

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error("Invalid response format from Google API")
    }

    return data.candidates[0].content.parts[0].text
  } catch (error) {
    console.error("Error calling Google Generative AI:", error)
    throw error
  }
}

export async function POST(req: NextRequest) {
  try {
    const { personalInfo, jobDescription }: RequestBody = await req.json()

    // Validate required fields
    if (!personalInfo.fullName || !personalInfo.email || !jobDescription) {
      return NextResponse.json(
        { error: "Missing required fields: name, email, and job description are required" },
        { status: 400 },
      )
    }

    // Enhanced Resume Prompt for AI-Enhanced Designer
    const resumePrompt = `
You are an expert resume writer specializing in ATS-friendly resumes for AI-enhanced designers and design interns. Create a professional, ATS-optimized resume for Tishbian Meshach S based on the following information:

CANDIDATE PROFILE:
- Name: ${personalInfo.fullName}
- Email: ${personalInfo.email}
- Phone: ${personalInfo.phone}
- Location: ${personalInfo.location}
- LinkedIn: ${personalInfo.linkedIn}
- Portfolio: ${personalInfo.portfolio}
- Summary: ${personalInfo.summary}
- Skills: ${personalInfo.skills}
- Experience: ${personalInfo.experience}
- Education: ${personalInfo.education}
- Certifications: ${personalInfo.certifications}

TARGET JOB DESCRIPTION:
${jobDescription}

SPECIAL FOCUS AREAS:
1. AI-Enhanced Design Skills: Highlight expertise in AI-powered design workflows, prompt engineering, and automation
2. Design Internship Optimization: Tailor for entry-level design positions and internships
3. Technical + Creative Balance: Showcase both technical skills (React, HTML, CSS) and creative abilities
4. Quantifiable AI Impact: Emphasize productivity improvements and efficiency gains through AI integration
5. Modern Design Tools: Focus on current industry-standard tools and AI-powered design platforms

ATS OPTIMIZATION REQUIREMENTS:
1. Use standard section headers: CONTACT INFORMATION, PROFESSIONAL SUMMARY, CORE COMPETENCIES, PROFESSIONAL EXPERIENCE, PROJECTS, EDUCATION, CERTIFICATIONS
2. Include relevant keywords from the job description naturally throughout the resume
3. Emphasize AI and automation skills as differentiators for modern design roles
4. Highlight measurable achievements (60% productivity increase, 50% faster iteration, 100+ projects)
5. Use action verbs: Led, Designed, Implemented, Enhanced, Developed, Created, Optimized
6. Format for ATS readability with clean structure and standard fonts
7. Include both traditional design skills and cutting-edge AI capabilities
8. Optimize for design internship keywords while showcasing advanced capabilities
9. Balance student status with professional experience and AI expertise
10. Ensure the resume passes ATS screening for design intern positions

UNIQUE VALUE PROPOSITIONS TO HIGHLIGHT:
- Combines traditional design expertise with cutting-edge AI capabilities
- Proven ability to increase team productivity through AI workflow integration
- Expert in prompt engineering for consistent, high-quality design outputs
- Pioneer in AI-assisted user research and design validation methodologies
- Specialized in creating scalable design systems using AI automation

Generate a complete, professional resume that positions Tishbian as an innovative, AI-enhanced designer perfect for modern design internships and entry-level positions.
`

    // Enhanced Cover Letter Prompt
    const coverLetterPrompt = `
You are an expert cover letter writer specializing in design internships and AI-enhanced creative roles. Create a compelling, personalized cover letter for Tishbian Meshach S based on the following information:

CANDIDATE PROFILE:
- Name: ${personalInfo.fullName}
- Email: ${personalInfo.email}
- Phone: ${personalInfo.phone}
- Location: ${personalInfo.location}
- LinkedIn: ${personalInfo.linkedIn}
- Portfolio: ${personalInfo.portfolio}
- Summary: ${personalInfo.summary}
- Skills: ${personalInfo.skills}
- Experience: ${personalInfo.experience}
- Education: ${personalInfo.education}
- Certifications: ${personalInfo.certifications}

TARGET JOB DESCRIPTION:
${jobDescription}

COVER LETTER REQUIREMENTS:
1. Professional business letter format with proper salutation
2. Opening paragraph: Express genuine enthusiasm for the specific role and company
3. Body paragraphs (2-3): 
   - Highlight relevant AI-enhanced design experience and achievements
   - Connect specific skills to job requirements
   - Showcase unique value proposition as an AI-powered designer
   - Provide concrete examples of impact (productivity gains, project successes)
4. Closing paragraph: Strong call to action and professional sign-off
5. Tone: Professional yet passionate, showcasing innovation and forward-thinking
6. Length: 250-400 words, concise but impactful
7. Personalization: Reference specific company details or job requirements when possible

KEY THEMES TO EMPHASIZE:
- Innovation in design through AI integration
- Proven track record of enhancing productivity and efficiency
- Bridge between traditional design skills and cutting-edge technology
- Student status as an advantage (fresh perspective, eagerness to learn)
- Ability to contribute immediately while growing with the company
- Passion for user-centered design enhanced by AI capabilities

UNIQUE SELLING POINTS:
- 4+ years of design experience despite being a student
- Leading designer role at TripXplo with measurable impact
- Expertise in prompt engineering and AI workflow automation
- 100+ successful freelance projects with AI-enhanced processes
- Combination of technical skills (React, HTML, CSS) with creative abilities
- Pioneer in AI-assisted design methodologies

Create an original, engaging cover letter that positions Tishbian as the ideal candidate for this design internship - someone who brings both fresh student energy and proven professional experience, enhanced by cutting-edge AI capabilities.
`

    console.log("Generating AI-enhanced resume with Gemini...")

    // Generate resume
    const resumeResult = await generateWithGemini(resumePrompt)

    console.log("Generating personalized cover letter with Gemini...")

    // Generate cover letter
    const coverLetterResult = await generateWithGemini(coverLetterPrompt)

    console.log("AI-enhanced content generation completed successfully")

    return NextResponse.json({
      resume: resumeResult,
      coverLetter: coverLetterResult,
    })
  } catch (error) {
    console.error("Error generating content:", error)

    // More specific error handling
    if (error instanceof Error) {
      if (error.message.includes("API key") || error.message.includes("401")) {
        return NextResponse.json(
          { error: "Invalid API key. Please check your Google AI API key configuration." },
          { status: 401 },
        )
      }
      if (error.message.includes("quota") || error.message.includes("429")) {
        return NextResponse.json({ error: "API quota exceeded. Please try again later." }, { status: 429 })
      }
      if (error.message.includes("400")) {
        return NextResponse.json({ error: "Invalid request format. Please try again." }, { status: 400 })
      }
    }

    return NextResponse.json({ error: "Failed to generate content. Please try again." }, { status: 500 })
  }
}
